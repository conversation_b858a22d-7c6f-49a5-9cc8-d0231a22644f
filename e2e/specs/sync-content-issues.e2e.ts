import {
  setupE2ETestHooks,
  expectNotice,
  executeCommand,
  expectPostFile
} from '../helpers/shared-context';
import { setupTestFailureHandler } from '../helpers/test-failure-handler';
import { TestPostManager } from '../helpers/ghost-api-helpers';


import { test, expect, describe, beforeEach, afterEach } from 'vitest';

// Setup screenshot capture on test failures
setupTestFailureHandler();

describe("Sync Content Issues", () => {
  const context = setupE2ETestHooks();
  let postManager: TestPostManager;

  beforeEach(async () => {
    postManager = new TestPostManager();
  });

  afterEach(async () => {
    // Clean up managed posts only after test completes
    if (postManager) {
      await postManager.cleanup();
    }
  });

  test("should import new posts without using sync path (no frontmatter cache errors)", async () => {
    // Create a test post in Ghost using the API
    const testPost = await postManager.createPostFromFixture('sync-content-test.md');

    // Verify the post was created
    expect(testPost).toBeTruthy();
    expect(testPost.title).toBe('Sync Content Test');
    expect(testPost.slug).toBe('sync-content-test');

    console.log('Created test post:', testPost.id, testPost.title);

    // Wait a moment for Ghost to process the post
    await context.page.waitForTimeout(2000);

    // Ensure the file does NOT exist yet (this is key to reproducing the original bug)
    const fileExistsBefore = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-content-test.md');
      return file !== null;
    });
    expect(fileExistsBefore).toBe(false);

    // Use the browse posts command directly
    await executeCommand(context, 'Browse and sync posts from Ghost');

    // Wait for the command to execute and modal to appear
    await context.page.waitForTimeout(3000);

    // Wait for the post suggestion to appear (we know from debug it's there)
    await context.page.waitForSelector('.ghost-post-suggestion', { timeout: 10000 });

    // Type the post slug to filter and select the test post
    await context.page.keyboard.type('sync-content-test');
    await context.page.keyboard.press('Enter');

    // Wait for the sync operation to complete
    // This should NOT throw the "No frontmatter found in file metadata cache" error
    await expectNotice(context, "Synced", 15000);

    // Verify the file was created in the articles directory
    const fileExists = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-content-test.md');
      return file !== null;
    });

    expect(fileExists).toBe(true);

    // Verify the file content contains the expected frontmatter and content
    const fileContent = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-content-test.md');
      if (file) {
        return app.vault.read(file);
      }
      return null;
    });

    expect(fileContent).toBeTruthy();
    expect(fileContent).toContain('Title: "Sync Content Test"');
    expect(fileContent).toContain('Slug: "sync-content-test"');
    expect(fileContent).toContain('# Sync Content Test');
  });



  test("should handle syncing existing file from Ghost post browser", async () => {
    // Create a test post in Ghost
    await postManager.createPostFromFixture('sync-existing-test.md');

    // Create a local file with the same slug first
    await context.page.evaluate(() => {
      const app = (window as any).app;
      const vault = app.vault;
      return vault.create('articles/sync-existing-test.md', `---
Title: "Old Title"
Slug: "sync-existing-test"
Status: "draft"
---

# Old Title

This is the old content.`);
    });

    // Wait for the file to be created
    await context.page.waitForTimeout(1000);

    // Use the browse posts command directly
    await executeCommand(context, 'Browse and sync posts from Ghost');

    // Wait for the post suggestion to appear
    await context.page.waitForSelector('.ghost-post-suggestion', { timeout: 10000 });

    // Type the post slug to filter and select the test post
    await context.page.keyboard.type('sync-existing-test');
    await context.page.keyboard.press('Enter');

    // Wait for the sync operation to complete
    await expectNotice(context, "Synced", 15000);

    // Verify the file content was updated with Ghost content
    const fileContent = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-existing-test.md');
      if (file) {
        return app.vault.read(file);
      }
      return null;
    });

    expect(fileContent).toBeTruthy();
    expect(fileContent).toContain('Title: "Sync Existing Test"');
    expect(fileContent).toContain('# Sync Existing Test');
    expect(fileContent).toContain('This is a test post for syncing existing files.');
  });

  test("should import post with fenced code blocks preserving all content", async () => {
    // Create a test post in Ghost with fenced code blocks
    const testPost = await postManager.createPostFromFixture('test-post.md');

    // Verify the post was created
    expect(testPost).toBeTruthy();
    expect(testPost.title).toBe('Test Post');

    // Ghost might modify the slug to avoid conflicts, so let's use the actual slug returned
    const actualSlug = testPost.slug;
    console.log('Created test post with code blocks:', testPost.id, testPost.title, 'slug:', actualSlug);

    // Wait a moment for Ghost to process the post
    await context.page.waitForTimeout(2000);

    // Verify the file doesn't exist locally before import
    const fileExistsBefore = await context.page.evaluate((slug) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(`articles/${slug}.md`);
      return file !== null;
    }, actualSlug);

    expect(fileExistsBefore).toBe(false);

    // Use the browse posts command directly
    await executeCommand(context, 'Browse and sync posts from Ghost');

    // Wait for the command to execute and modal to appear
    await context.page.waitForTimeout(3000);

    // Wait for the post suggestion to appear
    await context.page.waitForSelector('.ghost-post-suggestion', { timeout: 10000 });

    // Type the post slug to filter and select the test post
    await context.page.keyboard.type(actualSlug);
    await context.page.keyboard.press('Enter');

    // Wait for the sync operation to complete
    await expectNotice(context, "Synced", 15000);

    // Verify the file was created in the articles directory
    const fileExists = await context.page.evaluate((slug) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(`articles/${slug}.md`);
      return file !== null;
    }, actualSlug);

    expect(fileExists).toBe(true);

    // Get the filename without extension for expectPostFile
    const filenameWithoutExt = actualSlug;

    // Use expectPostFile helper to verify the content structure and fenced code blocks
    await expectPostFile(context, filenameWithoutExt, {
      title: "Test Post",
      slug: actualSlug,
      content: "Hello World",
      timeout: 10000
    });

    // Verify the fenced code block is preserved
    await expectPostFile(context, filenameWithoutExt, {
      content: "```elixir"
    });

    await expectPostFile(context, filenameWithoutExt, {
      content: "defmodule Hello do"
    });

    await expectPostFile(context, filenameWithoutExt, {
      content: 'def hi, do: "HI"'
    });

    await expectPostFile(context, filenameWithoutExt, {
      content: "end"
    });

    await expectPostFile(context, filenameWithoutExt, {
      content: "```"
    });

    // Verify the content after the code block is preserved
    await expectPostFile(context, filenameWithoutExt, {
      content: "That's it."
    });

    // Get the full content to verify the complete structure and debug any issues
    const fileContent = await context.page.evaluate((slug) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(`articles/${slug}.md`);
      if (file) {
        return app.vault.read(file);
      }
      return null;
    }, actualSlug);

    console.log('📄 Full imported file content:');
    console.log('=====================================');
    console.log(fileContent);
    console.log('=====================================');

    expect(fileContent).toBeTruthy();
    expect(fileContent).toContain('Title: "Test Post"');
    expect(fileContent).toContain(`Slug: "${actualSlug}"`);
    expect(fileContent).toContain('# Test Post');
    expect(fileContent).toContain('Hello World');
    expect(fileContent).toContain('```elixir');
    expect(fileContent).toContain('defmodule Hello do');
    expect(fileContent).toContain('def hi, do: "HI"');
    expect(fileContent).toContain('end');
    expect(fileContent).toContain('```');
    expect(fileContent).toContain("That's it.");

    // Verify that the content after the code block is actually present
    // This is the key test - if the bug exists, "That's it." should be missing
    if (!fileContent.includes("That's it.")) {
      console.error('❌ BUG REPRODUCED: Content after fenced code block is missing!');
      console.error('Expected to find: "That\'s it."');
      console.error('But it was not found in the imported content.');
      throw new Error('Content after fenced code block was truncated during import');
    }

    console.log('✅ Test post with fenced code blocks imported successfully - all content preserved');
  });

  test("should import manually created post with fenced code blocks (simulating Ghost editor)", async () => {
    // Create a test post with raw lexical content that simulates what Ghost editor might create
    // This simulates the scenario where a user manually creates a post in Ghost's editor
    const rawLexicalContent = {
      "root": {
        "children": [
          {
            "children": [
              {
                "detail": 0,
                "format": 0,
                "mode": "normal",
                "style": "",
                "text": "Hello World",
                "type": "text",
                "version": 1
              }
            ],
            "direction": "ltr",
            "format": "",
            "indent": 0,
            "type": "paragraph",
            "version": 1
          },
          {
            "children": [
              {
                "detail": 0,
                "format": 0,
                "mode": "normal",
                "style": "",
                "text": "defmodule Hello do\n  def hi, do: \"HI\"\nend",
                "type": "text",
                "version": 1
              }
            ],
            "direction": "ltr",
            "format": "",
            "indent": 0,
            "language": "elixir",
            "type": "code",
            "version": 1
          },
          {
            "children": [
              {
                "detail": 0,
                "format": 0,
                "mode": "normal",
                "style": "",
                "text": "That's it.",
                "type": "text",
                "version": 1
              }
            ],
            "direction": "ltr",
            "format": "",
            "indent": 0,
            "type": "paragraph",
            "version": 1
          }
        ],
        "direction": "ltr",
        "format": "",
        "indent": 0,
        "type": "root",
        "version": 1
      }
    };

    // Create the post directly using Ghost API with raw lexical content
    if (!postManager.isAvailable()) {
      console.log('⚠️ Ghost API not available, skipping manual post test');
      return;
    }

    const ghostAPI = postManager.getAPI();
    const testPost = await ghostAPI.createPost({
      title: "Manual Test Post",
      slug: "manual-test-post-with-code",
      status: "draft",
      lexical: JSON.stringify(rawLexicalContent),
      visibility: "public",
      featured: false
    });

    // Track for cleanup
    postManager.trackPostForCleanup(testPost.id);

    console.log('Created manual test post with raw lexical:', testPost.id, testPost.title, 'slug:', testPost.slug);

    // Wait a moment for Ghost to process the post
    await context.page.waitForTimeout(2000);

    // Verify the file doesn't exist locally before import
    const fileExistsBefore = await context.page.evaluate((slug) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(`articles/${slug}.md`);
      return file !== null;
    }, testPost.slug);

    expect(fileExistsBefore).toBe(false);

    // Use the browse posts command directly
    await executeCommand(context, 'Browse and sync posts from Ghost');

    // Wait for the command to execute and modal to appear
    await context.page.waitForTimeout(3000);

    // Wait for the post suggestion to appear
    await context.page.waitForSelector('.ghost-post-suggestion', { timeout: 10000 });

    // Type the post slug to filter and select the test post
    await context.page.keyboard.type(testPost.slug);
    await context.page.keyboard.press('Enter');

    // Wait for the sync operation to complete
    await expectNotice(context, "Synced", 15000);

    // Get the full content to verify the complete structure and debug any issues
    const fileContent = await context.page.evaluate((slug) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(`articles/${slug}.md`);
      if (file) {
        return app.vault.read(file);
      }
      return null;
    }, testPost.slug);

    console.log('📄 Full imported manual post content:');
    console.log('=====================================');
    console.log(fileContent);
    console.log('=====================================');

    expect(fileContent).toBeTruthy();
    expect(fileContent).toContain('Title: "Manual Test Post"');
    expect(fileContent).toContain(`Slug: "${testPost.slug}"`);
    expect(fileContent).toContain('Hello World');
    expect(fileContent).toContain('```elixir');
    expect(fileContent).toContain('defmodule Hello do');
    expect(fileContent).toContain('def hi, do: "HI"');
    expect(fileContent).toContain('end');
    expect(fileContent).toContain('```');
    expect(fileContent).toContain("That's it.");

    // Verify that the content after the code block is actually present
    // This is the key test - if the bug exists, "That's it." should be missing
    if (!fileContent.includes("That's it.")) {
      console.error('❌ BUG REPRODUCED: Content after fenced code block is missing in manual post!');
      console.error('Expected to find: "That\'s it."');
      console.error('But it was not found in the imported content.');
      throw new Error('Content after fenced code block was truncated during import of manual post');
    }

    console.log('✅ Manual test post with fenced code blocks imported successfully - all content preserved');
  });

  test("should reproduce the actual bug with API-created post containing paragraphs and code blocks", async () => {
    // Create a post via API that will cause the Lexical parsing error
    // This reproduces the exact scenario you described

    // Create the post using our API (which will generate lexical content)
    const testPost = await postManager.createPostFromFixture('test-post.md');

    // But let's override it with a more complex post that matches your scenario
    if (!postManager.isAvailable()) {
      console.log('⚠️ Ghost API not available, skipping bug reproduction test');
      return;
    }

    const ghostAPI = postManager.getAPI();

    // Delete the simple test post and create a more complex one
    await ghostAPI.deletePost(testPost.id);

    // Create a post with the exact content that's causing issues
    // Use our ContentConverter to generate lexical content the same way the plugin does
    const markdownContent = `# Introducing Elixir Drops

This is a paragraph before the code block.

\`\`\`elixir
defmodule Hello do
  def hi, do: "HI"
end
\`\`\`

This is a paragraph after the code block that should be preserved.

Another paragraph to make sure we have multiple paragraphs.`;

    const frontMatter = {
      title: "Introducing Elixir Drops",
      slug: "introducing-elixir-drops-bug-test",
      status: "draft",
      visibility: "public",
      featured: false
    };

    // Use ContentConverter to create Ghost post data the same way the plugin does
    const { ContentConverter } = await import('../../src/utils/content-converter');
    const postData = await ContentConverter.createGhostPostData(frontMatter, markdownContent);

    console.log('📄 Generated lexical content for post:');
    console.log('=====================================');
    console.log(JSON.stringify(JSON.parse(postData.lexical), null, 2));
    console.log('=====================================');

    const complexPost = await ghostAPI.createPost(postData);

    // Track for cleanup
    postManager.trackPostForCleanup(complexPost.id);

    console.log('Created complex test post:', complexPost.id, complexPost.title, 'slug:', complexPost.slug);

    // Wait a moment for Ghost to process the post and generate lexical content
    await context.page.waitForTimeout(3000);

    // Get the post back to see what lexical content Ghost generated
    const retrievedPost = await ghostAPI.getPostById(complexPost.id);
    console.log('📄 Ghost-generated lexical content:');
    console.log('=====================================');
    console.log(JSON.stringify(JSON.parse(retrievedPost.lexical), null, 2));
    console.log('=====================================');

    // Verify the file doesn't exist locally before import
    const fileExistsBefore = await context.page.evaluate((slug) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(`articles/${slug}.md`);
      return file !== null;
    }, complexPost.slug);

    expect(fileExistsBefore).toBe(false);

    // Use the browse posts command directly
    await executeCommand(context, 'Browse and sync posts from Ghost');

    // Wait for the command to execute and modal to appear
    await context.page.waitForTimeout(3000);

    // Wait for the post suggestion to appear
    await context.page.waitForSelector('.ghost-post-suggestion', { timeout: 10000 });

    // Type the post slug to filter and select the test post
    await context.page.keyboard.type(complexPost.slug);
    await context.page.keyboard.press('Enter');

    // Wait for the sync operation to complete (or fail)
    await expectNotice(context, "Synced", 15000);

    // Get the full content to verify what was actually imported
    const fileContent = await context.page.evaluate((slug) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(`articles/${slug}.md`);
      if (file) {
        return app.vault.read(file);
      }
      return null;
    }, complexPost.slug);

    console.log('📄 Full imported complex post content:');
    console.log('=====================================');
    console.log(fileContent);
    console.log('=====================================');

    expect(fileContent).toBeTruthy();
    expect(fileContent).toContain('Title: "Introducing Elixir Drops"');
    expect(fileContent).toContain(`Slug: "${complexPost.slug}"`);
    expect(fileContent).toContain('# Introducing Elixir Drops');
    expect(fileContent).toContain('This is a paragraph before the code block.');
    expect(fileContent).toContain('```elixir');
    expect(fileContent).toContain('defmodule Hello do');
    expect(fileContent).toContain('def hi, do: "HI"');
    expect(fileContent).toContain('end');
    expect(fileContent).toContain('```');

    // This is the critical test - verify content after code block is preserved
    expect(fileContent).toContain('This is a paragraph after the code block that should be preserved.');
    expect(fileContent).toContain('Another paragraph to make sure we have multiple paragraphs.');

    // If any content is missing, this test should fail and show us exactly what's wrong
    if (!fileContent.includes('This is a paragraph after the code block that should be preserved.')) {
      console.error('❌ BUG REPRODUCED: Content after code block is missing!');
      console.error('Expected: "This is a paragraph after the code block that should be preserved."');
      console.error('But it was not found in the imported content.');
      throw new Error('Content after code block was truncated - bug reproduced!');
    }

    if (!fileContent.includes('Another paragraph to make sure we have multiple paragraphs.')) {
      console.error('❌ BUG REPRODUCED: Final paragraph is missing!');
      console.error('Expected: "Another paragraph to make sure we have multiple paragraphs."');
      console.error('But it was not found in the imported content.');
      throw new Error('Final paragraph was truncated - bug reproduced!');
    }

    console.log('✅ Complex post with multiple paragraphs and code blocks imported successfully');
  });

  test("should reproduce the lexical parsing error with problematic code block structure", async () => {
    // Create a post with the EXACT lexical content structure from Ghost
    // This is the actual structure from code-block-test-post.json
    const problematicLexicalContent = {
      "root": {
        "children": [
          {
            "children": [
              {
                "detail": 0,
                "format": 0,
                "mode": "normal",
                "style": "",
                "text": "Hello World",
                "type": "extended-text",
                "version": 1
              }
            ],
            "direction": "ltr",
            "format": "",
            "indent": 0,
            "type": "paragraph",
            "version": 1
          },
          {
            "type": "codeblock",
            "version": 1,
            "code": "defmodule Hello do\n  def hi, do: \"HI\"\nend",
            "language": "elixir",
            "caption": ""
          },
          {
            "children": [
              {
                "detail": 0,
                "format": 0,
                "mode": "normal",
                "style": "",
                "text": "That's it.",
                "type": "extended-text",
                "version": 1
              }
            ],
            "direction": "ltr",
            "format": "",
            "indent": 0,
            "type": "paragraph",
            "version": 1
          }
        ],
        "direction": "ltr",
        "format": "",
        "indent": 0,
        "type": "root",
        "version": 1
      }
    };

    // Create the post directly using Ghost API with problematic lexical content
    if (!postManager.isAvailable()) {
      console.log('⚠️ Ghost API not available, skipping problematic lexical test');
      return;
    }

    const ghostAPI = postManager.getAPI();
    const testPost = await ghostAPI.createPost({
      title: "Problematic Lexical Test Post",
      slug: "problematic-lexical-test-post",
      status: "draft",
      lexical: JSON.stringify(problematicLexicalContent),
      visibility: "public",
      featured: false
    });

    // Track for cleanup
    postManager.trackPostForCleanup(testPost.id);

    console.log('Created problematic lexical test post:', testPost.id, testPost.title, 'slug:', testPost.slug);

    // Wait a moment for Ghost to process the post
    await context.page.waitForTimeout(2000);

    // Verify the file doesn't exist locally before import
    const fileExistsBefore = await context.page.evaluate((slug) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(`articles/${slug}.md`);
      return file !== null;
    }, testPost.slug);

    expect(fileExistsBefore).toBe(false);

    // Use the browse posts command directly
    await executeCommand(context, 'Browse and sync posts from Ghost');

    // Wait for the command to execute and modal to appear
    await context.page.waitForTimeout(3000);

    // Wait for the post suggestion to appear
    await context.page.waitForSelector('.ghost-post-suggestion', { timeout: 10000 });

    // Type the post slug to filter and select the test post
    await context.page.keyboard.type(testPost.slug);
    await context.page.keyboard.press('Enter');

    // Wait for the sync operation to complete (this should trigger the Lexical error)
    await expectNotice(context, "Synced", 15000);

    // Capture console messages to see any errors
    const consoleMessages = await context.page.evaluate(() => {
      // Get console messages from the page if they're stored
      return (window as any).ghostSyncDebugMessages || [];
    });
    console.log('🔍 Browser console messages:', consoleMessages);

    // Get the full content to verify what was actually imported
    const fileContent = await context.page.evaluate((slug) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(`articles/${slug}.md`);
      if (file) {
        return app.vault.read(file);
      }
      return null;
    }, testPost.slug);

    console.log('📄 Full imported problematic post content:');
    console.log('=====================================');
    console.log(fileContent);
    console.log('=====================================');

    expect(fileContent).toBeTruthy();
    expect(fileContent).toContain('Title: "Problematic Lexical Test Post"');
    expect(fileContent).toContain(`Slug: "${testPost.slug}"`);
    expect(fileContent).toContain('Hello World');

    // This is the critical test - verify that our fix works and content is preserved
    expect(fileContent).toContain("That's it.");
    expect(fileContent).toContain('```elixir');
    expect(fileContent).toContain('defmodule Hello do');
    expect(fileContent).toContain('def hi, do: "HI"');
    expect(fileContent).toContain('end');
    expect(fileContent).toContain('```');

    console.log('✅ Problematic lexical post imported successfully - fix is working!');
  });
});
