#!/usr/bin/env node

/**
 * Clean, simple script to manage ONE formatting test post in Ghost
 * This post contains all the formatting we want to test and serves as our Lexical reference
 */

const GhostAdminAPI = require('@tryghost/admin-api');
const fsPromises = require('fs').promises;
const path = require('path');

// Ghost configuration
const GHOST_CONFIG = {
  url: 'https://obsidian-plugin.ghost.io',
  key: '68a42987d4ff6d0001ea507c:14819197326fe3f39d49d4fe0e060f2c34fb68bb1be94c66c2a2bb252f93f797'
};

// Test post configuration
const TEST_POST = {
  title: 'Code Block Test Post',
  slug: 'code-block-test-post',
  status: 'draft',
  visibility: 'public'
};

// Simple HTML content for testing
function getBasicHTMLContent() {
  return `<h1>Code Block Test Post</h1>
<p>Hello World</p>
<p>This is a paragraph before the code block.</p>
<p>That's it.</p>`;
}

class GhostFormattingTest {
  constructor() {
    this.api = new GhostAdminAPI({
      url: GHOST_CONFIG.url,
      key: GHOST_CONFIG.key,
      version: 'v6.0'
    });
  }

  async createOrUpdateTestPost() {
    console.log('🚀 Managing formatting test post in Ghost...');

    try {
      // Check if test post exists
      const existingPosts = await this.api.posts.browse({
        filter: `slug:${TEST_POST.slug}`,
        limit: 1
      });

      let post;

      const htmlContent = getBasicHTMLContent();

      if (existingPosts.length > 0) {
        console.log('📝 Updating existing test post...');
        post = await this.api.posts.edit({
          id: existingPosts[0].id,
          ...TEST_POST,
          html: htmlContent,
          mobiledoc: null,
          updated_at: existingPosts[0].updated_at
        });
        console.log('✅ Test post updated');
      } else {
        console.log('📝 Creating new test post...');
        post = await this.api.posts.add({
          ...TEST_POST,
          html: htmlContent
        });
        console.log('✅ Test post created');
      }

      console.log(`📄 Post ID: ${post.id}`);
      console.log(`🔗 Post URL: ${GHOST_CONFIG.url}/ghost/#/editor/post/${post.id}`);

      return post;
    } catch (error) {
      console.error('❌ Error managing test post:', error);
      throw error;
    }
  }

  async fetchLexicalStructure() {
    console.log('📥 Fetching Lexical structure from Ghost...');

    try {
      const posts = await this.api.posts.browse({
        filter: `slug:${TEST_POST.slug}`,
        limit: 1
      });

      if (posts.length === 0) {
        throw new Error('Test post not found. Run with "create" first.');
      }

      const post = await this.api.posts.read(
        { id: posts[0].id },
        { formats: ['lexical'] }
      );

      console.log('✅ Complete post data fetched');

      // Save complete post JSON to reference file
      const referenceDir = path.join(process.cwd(), 'tests/fixtures/lexical-reference');
      await fsPromises.mkdir(referenceDir, { recursive: true });

      const postFile = path.join(referenceDir, `${TEST_POST.slug}.json`);
      await fsPromises.writeFile(postFile, JSON.stringify(post, null, 2));

      console.log('✅ Complete post reference saved to:', postFile);
      console.log(`📊 Post data size: ${JSON.stringify(post).length} characters`);
      console.log(`📊 Lexical size: ${post.lexical ? post.lexical.length : 0} characters`);

      return post;
    } catch (error) {
      console.error('❌ Error fetching Lexical structure:', error);
      throw error;
    }
  }

  async cleanupOldPosts() {
    console.log('🧹 Cleaning up old test posts...');

    try {
      const posts = await this.api.posts.browse({
        filter: 'slug:obsidian-ghost-sync-test-post,slug:lexical-structure-test',
        limit: 'all'
      });

      for (const post of posts) {
        console.log(`🗑️ Deleting old post: ${post.title}`);
        await this.api.posts.delete({ id: post.id });
      }

      console.log(`✅ Cleaned up ${posts.length} old posts`);
    } catch (error) {
      console.error('❌ Error cleaning up:', error);
    }
  }
}

// CLI interface
async function main() {
  const command = process.argv[2] || 'help';
  const manager = new GhostFormattingTest();

  try {
    switch (command) {
      case 'create':
        await manager.createOrUpdateTestPost();
        break;

      case 'fetch':
        await manager.fetchLexicalStructure();
        break;

      case 'both':
        await manager.createOrUpdateTestPost();
        await manager.fetchLexicalStructure();
        break;

      case 'cleanup':
        await manager.cleanupOldPosts();
        break;

      default:
        console.log('Usage: node ghost-formatting-test.js [create|fetch|both|cleanup]');
        console.log('  create  - Create/update the code block test post');
        console.log('  fetch   - Fetch Lexical structure from existing post');
        console.log('  both    - Create/update post and fetch structure');
        console.log('  cleanup - Remove old test posts');
        console.log('');
        console.log('After creating the post, manually add code blocks in Ghost editor,');
        console.log('then run "fetch" to see the actual lexical structure.');
        break;
    }
  } catch (error) {
    console.error('💥 Failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = GhostFormattingTest;
